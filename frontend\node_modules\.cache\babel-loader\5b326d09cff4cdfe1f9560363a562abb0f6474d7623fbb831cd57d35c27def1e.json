{"ast": null, "code": "var _jsxFileName = \"D:\\\\work\\\\python_work\\\\Family_Takeout\\\\frontend\\\\src\\\\index.tsx\";\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst root = ReactDOM.createRoot(document.getElementById('root'));\nroot.render(/*#__PURE__*/_jsxDEV(React.StrictMode, {\n  children: /*#__PURE__*/_jsxDEV(App, {}, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 7,\n    columnNumber: 5\n  }, this)\n}, void 0, false, {\n  fileName: _jsxFileName,\n  lineNumber: 6,\n  columnNumber: 3\n}, this));\n\n// If you want to start measuring performance in your app, pass a function\n// to log results (for example: reportWebVitals(console.log))\n// or send to an analytics endpoint. Learn more: https://bit.ly/CRA-vitals\nreportWebVitals();", "map": {"version": 3, "names": ["root", "ReactDOM", "createRoot", "document", "getElementById", "render", "_jsxDEV", "React", "StrictMode", "children", "App", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "reportWebVitals"], "sources": ["D:/work/python_work/Family_Takeout/frontend/src/index.tsx"], "sourcesContent": ["\r\nconst root = ReactDOM.createRoot(\r\n  document.getElementById('root') as HTMLElement\r\n);\r\nroot.render(\r\n  <React.StrictMode>\r\n    <App />\r\n  </React.StrictMode>\r\n);\r\n\r\n// If you want to start measuring performance in your app, pass a function\r\n// to log results (for example: reportWebVitals(console.log))\r\n// or send to an analytics endpoint. Learn more: https://bit.ly/CRA-vitals\r\nreportWebVitals();"], "mappings": ";;AACA,MAAMA,IAAI,GAAGC,QAAQ,CAACC,UAAU,CAC9BC,QAAQ,CAACC,cAAc,CAAC,MAAM,CAChC,CAAC;AACDJ,IAAI,CAACK,MAAM,cACTC,OAAA,CAACC,KAAK,CAACC,UAAU;EAAAC,QAAA,eACfH,OAAA,CAACI,GAAG;IAAAC,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAAE;AAAC;EAAAH,QAAA,EAAAC,YAAA;EAAAC,UAAA;EAAAC,YAAA;AAAA,OACS,CACpB,CAAC;;AAED;AACA;AACA;AACAC,eAAe,CAAC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}