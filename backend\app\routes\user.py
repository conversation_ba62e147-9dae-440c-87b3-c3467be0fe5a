from flask import Blueprint, request, jsonify
from app.models.user import User, Address
from app import db
from flask_jwt_extended import jwt_required, get_jwt_identity
from datetime import datetime

user_bp = Blueprint('user', __name__)

@user_bp.route('/profile', methods=['PUT'])
@jwt_required()
def update_profile():
    """更新用户信息"""
    user_id = get_jwt_identity()
    user = User.query.get(user_id)
    
    if not user:
        return jsonify({"success": False, "message": "用户不存在"}), 404
    
    data = request.get_json()
    
    if not data:
        return jsonify({"success": False, "message": "请求数据不能为空"}), 400
    
    # 更新基本信息
    if 'nickname' in data:
        user.nickname = data['nickname']
    
    if 'gender' in data:
        user.gender = data['gender']
    
    if 'birth_date' in data:
        try:
            user.birth_date = datetime.strptime(data['birth_date'], '%Y-%m-%d').date()
        except ValueError:
            return jsonify({"success": False, "message": "生日格式不正确"}), 400
    
    if 'avatar' in data:
        user.avatar = data['avatar']
    
    user.updated_at = datetime.utcnow()
    db.session.commit()
    
    return jsonify({
        "success": True,
        "message": "用户信息更新成功",
        "user": user.to_dict()
    }), 200

@user_bp.route('/addresses', methods=['GET'])
@jwt_required()
def get_addresses():
    """获取用户收货地址列表"""
    user_id = get_jwt_identity()
    addresses = Address.query.filter_by(user_id=user_id).all()
    
    return jsonify({
        "success": True,
        "addresses": [address.to_dict() for address in addresses]
    }), 200

@user_bp.route('/addresses', methods=['POST'])
@jwt_required()
def create_address():
    """创建收货地址"""
    user_id = get_jwt_identity()
    data = request.get_json()
    
    if not data:
        return jsonify({"success": False, "message": "请求数据不能为空"}), 400
    
    required_fields = ['receiver_name', 'receiver_phone', 'province', 'city', 'district', 'detail_address']
    for field in required_fields:
        if not data.get(field):
            return jsonify({"success": False, "message": f"{field}不能为空"}), 400
    
    # 如果设置为默认地址，先取消其他默认地址
    if data.get('is_default', False):
        Address.query.filter_by(user_id=user_id, is_default=True).update({'is_default': False})
    
    address = Address(
        user_id=user_id,
        receiver_name=data['receiver_name'],
        receiver_phone=data['receiver_phone'],
        province=data['province'],
        city=data['city'],
        district=data['district'],
        detail_address=data['detail_address'],
        postal_code=data.get('postal_code'),
        label=data.get('label'),
        is_default=data.get('is_default', False)
    )
    
    db.session.add(address)
    db.session.commit()
    
    return jsonify({
        "success": True,
        "message": "地址创建成功",
        "address": address.to_dict()
    }), 201

@user_bp.route('/addresses/<int:address_id>', methods=['PUT'])
@jwt_required()
def update_address(address_id):
    """更新收货地址"""
    user_id = get_jwt_identity()
    address = Address.query.filter_by(id=address_id, user_id=user_id).first()
    
    if not address:
        return jsonify({"success": False, "message": "地址不存在"}), 404
    
    data = request.get_json()
    
    if not data:
        return jsonify({"success": False, "message": "请求数据不能为空"}), 400
    
    # 更新地址信息
    if 'receiver_name' in data:
        address.receiver_name = data['receiver_name']
    
    if 'receiver_phone' in data:
        address.receiver_phone = data['receiver_phone']
    
    if 'province' in data:
        address.province = data['province']
    
    if 'city' in data:
        address.city = data['city']
    
    if 'district' in data:
        address.district = data['district']
    
    if 'detail_address' in data:
        address.detail_address = data['detail_address']
    
    if 'postal_code' in data:
        address.postal_code = data['postal_code']
    
    if 'label' in data:
        address.label = data['label']
    
    # 处理默认地址设置
    if 'is_default' in data:
        if data['is_default']:
            # 取消其他默认地址
            Address.query.filter_by(user_id=user_id, is_default=True).update({'is_default': False})
        address.is_default = data['is_default']
    
    address.updated_at = datetime.utcnow()
    db.session.commit()
    
    return jsonify({
        "success": True,
        "message": "地址更新成功",
        "address": address.to_dict()
    }), 200

@user_bp.route('/addresses/<int:address_id>', methods=['DELETE'])
@jwt_required()
def delete_address(address_id):
    """删除收货地址"""
    user_id = get_jwt_identity()
    address = Address.query.filter_by(id=address_id, user_id=user_id).first()
    
    if not address:
        return jsonify({"success": False, "message": "地址不存在"}), 404
    
    db.session.delete(address)
    db.session.commit()
    
    return jsonify({
        "success": True,
        "message": "地址删除成功"
    }), 200

@user_bp.route('/addresses/<int:address_id>/set-default', methods=['PUT'])
@jwt_required()
def set_default_address(address_id):
    """设置默认地址"""
    user_id = get_jwt_identity()
    address = Address.query.filter_by(id=address_id, user_id=user_id).first()
    
    if not address:
        return jsonify({"success": False, "message": "地址不存在"}), 404
    
    # 取消其他默认地址
    Address.query.filter_by(user_id=user_id, is_default=True).update({'is_default': False})
    
    # 设置当前地址为默认
    address.is_default = True
    address.updated_at = datetime.utcnow()
    db.session.commit()
    
    return jsonify({
        "success": True,
        "message": "默认地址设置成功",
        "address": address.to_dict()
    }), 200

@user_bp.route('/orders', methods=['GET'])
@jwt_required()
def get_orders():
    """获取用户订单列表"""
    user_id = get_jwt_identity()
    page = request.args.get('page', 1, type=int)
    per_page = request.args.get('per_page', 10, type=int)
    
    orders = Order.query.filter_by(user_id=user_id)\
                       .order_by(Order.created_at.desc())\
                       .paginate(page=page, per_page=per_page, error_out=False)
    
    return jsonify({
        "success": True,
        "orders": [order.to_dict() for order in orders.items],
        "pagination": {
            "page": page,
            "per_page": per_page,
            "total": orders.total,
            "pages": orders.pages
        }
    }), 200

@user_bp.route('/orders/<int:order_id>', methods=['GET'])
@jwt_required()
def get_order_detail(order_id):
    """获取订单详情"""
    user_id = get_jwt_identity()
    order = Order.query.filter_by(id=order_id, user_id=user_id).first()
    
    if not order:
        return jsonify({"success": False, "message": "订单不存在"}), 404
    
    return jsonify({
        "success": True,
        "order": order.to_dict()
    }), 200 