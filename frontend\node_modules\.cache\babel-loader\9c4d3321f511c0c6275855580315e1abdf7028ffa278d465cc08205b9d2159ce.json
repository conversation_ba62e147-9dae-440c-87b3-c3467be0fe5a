{"ast": null, "code": "'use strict';\n\n// TODO: remove from `core-js@4`\nrequire('../modules/esnext.global-this');\nvar parent = require('../actual/global-this');\nmodule.exports = parent;", "map": {"version": 3, "names": ["require", "parent", "module", "exports"], "sources": ["D:/work/python_work/Family_Takeout/frontend/node_modules/core-js-pure/full/global-this.js"], "sourcesContent": ["'use strict';\n// TODO: remove from `core-js@4`\nrequire('../modules/esnext.global-this');\n\nvar parent = require('../actual/global-this');\n\nmodule.exports = parent;\n"], "mappings": "AAAA,YAAY;;AACZ;AACAA,OAAO,CAAC,+BAA+B,CAAC;AAExC,IAAIC,MAAM,GAAGD,OAAO,CAAC,uBAAuB,CAAC;AAE7CE,MAAM,CAACC,OAAO,GAAGF,MAAM", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}