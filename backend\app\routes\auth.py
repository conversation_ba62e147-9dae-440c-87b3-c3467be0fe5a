from flask import Blueprint, request, jsonify
from app.services.auth_service import AuthService
from app.models.user import User
from flask_jwt_extended import jwt_required, get_jwt_identity

auth_bp = Blueprint('auth', __name__)

@auth_bp.route('/register/email', methods=['POST'])
def register_with_email():
    """邮箱注册"""
    data = request.get_json()
    
    if not data:
        return jsonify({"success": False, "message": "请求数据不能为空"}), 400
    
    email = data.get('email')
    password = data.get('password')
    username = data.get('username')
    
    if not email or not password:
        return jsonify({"success": False, "message": "邮箱和密码不能为空"}), 400
    
    if len(password) < 6:
        return jsonify({"success": False, "message": "密码长度不能少于6位"}), 400
    
    result = AuthService.register_with_email(email, password, username)
    
    if result["success"]:
        return jsonify(result), 201
    else:
        return jsonify(result), 400

@auth_bp.route('/register/phone', methods=['POST'])
def register_with_phone():
    """手机号注册"""
    data = request.get_json()
    
    if not data:
        return jsonify({"success": False, "message": "请求数据不能为空"}), 400
    
    phone = data.get('phone')
    password = data.get('password')
    username = data.get('username')
    
    if not phone or not password:
        return jsonify({"success": False, "message": "手机号和密码不能为空"}), 400
    
    if len(password) < 6:
        return jsonify({"success": False, "message": "密码长度不能少于6位"}), 400
    
    result = AuthService.register_with_phone(phone, password, username)
    
    if result["success"]:
        return jsonify(result), 201
    else:
        return jsonify(result), 400

@auth_bp.route('/login/email', methods=['POST'])
def login_with_email():
    """邮箱登录"""
    data = request.get_json()
    
    if not data:
        return jsonify({"success": False, "message": "请求数据不能为空"}), 400
    
    email = data.get('email')
    password = data.get('password')
    
    if not email or not password:
        return jsonify({"success": False, "message": "邮箱和密码不能为空"}), 400
    
    result = AuthService.login_with_email(email, password)
    
    if result["success"]:
        return jsonify(result), 200
    else:
        return jsonify(result), 401

@auth_bp.route('/login/phone', methods=['POST'])
def login_with_phone():
    """手机号登录"""
    data = request.get_json()
    
    if not data:
        return jsonify({"success": False, "message": "请求数据不能为空"}), 400
    
    phone = data.get('phone')
    password = data.get('password')
    
    if not phone or not password:
        return jsonify({"success": False, "message": "手机号和密码不能为空"}), 400
    
    result = AuthService.login_with_phone(phone, password)
    
    if result["success"]:
        return jsonify(result), 200
    else:
        return jsonify(result), 401

@auth_bp.route('/send-email-code', methods=['POST'])
def send_email_verification():
    """发送邮箱验证码"""
    data = request.get_json()
    
    if not data:
        return jsonify({"success": False, "message": "请求数据不能为空"}), 400
    
    email = data.get('email')
    
    if not email:
        return jsonify({"success": False, "message": "邮箱不能为空"}), 400
    
    if not AuthService.validate_email(email):
        return jsonify({"success": False, "message": "邮箱格式不正确"}), 400
    
    code = AuthService.generate_verification_code()
    
    if AuthService.send_email_verification(email, code):
        AuthService.store_verification_code(email, code)
        return jsonify({"success": True, "message": "验证码已发送到邮箱"}), 200
    else:
        return jsonify({"success": False, "message": "验证码发送失败"}), 500

@auth_bp.route('/send-sms-code', methods=['POST'])
def send_sms_verification():
    """发送短信验证码"""
    data = request.get_json()
    
    if not data:
        return jsonify({"success": False, "message": "请求数据不能为空"}), 400
    
    phone = data.get('phone')
    
    if not phone:
        return jsonify({"success": False, "message": "手机号不能为空"}), 400
    
    if not AuthService.validate_phone(phone):
        return jsonify({"success": False, "message": "手机号格式不正确"}), 400
    
    code = AuthService.generate_verification_code()
    
    if AuthService.send_sms_verification(phone, code):
        AuthService.store_verification_code(phone, code)
        return jsonify({"success": True, "message": "验证码已发送到手机"}), 200
    else:
        return jsonify({"success": False, "message": "验证码发送失败"}), 500

@auth_bp.route('/login/verification-code', methods=['POST'])
def login_with_verification_code():
    """验证码登录"""
    data = request.get_json()
    
    if not data:
        return jsonify({"success": False, "message": "请求数据不能为空"}), 400
    
    phone = data.get('phone')
    code = data.get('code')
    
    if not phone or not code:
        return jsonify({"success": False, "message": "手机号和验证码不能为空"}), 400
    
    result = AuthService.login_with_verification_code(phone, code)
    
    if result["success"]:
        return jsonify(result), 200
    else:
        return jsonify(result), 401

@auth_bp.route('/login/wechat', methods=['POST'])
def wechat_login():
    """微信登录"""
    data = request.get_json()
    
    if not data:
        return jsonify({"success": False, "message": "请求数据不能为空"}), 400
    
    code = data.get('code')
    
    if not code:
        return jsonify({"success": False, "message": "微信授权码不能为空"}), 400
    
    result = AuthService.wechat_login(code)
    
    if result["success"]:
        return jsonify(result), 200
    else:
        return jsonify(result), 401

@auth_bp.route('/login/alipay', methods=['POST'])
def alipay_login():
    """支付宝登录"""
    data = request.get_json()
    
    if not data:
        return jsonify({"success": False, "message": "请求数据不能为空"}), 400
    
    auth_code = data.get('auth_code')
    
    if not auth_code:
        return jsonify({"success": False, "message": "支付宝授权码不能为空"}), 400
    
    result = AuthService.alipay_login(auth_code)
    
    if result["success"]:
        return jsonify(result), 200
    else:
        return jsonify(result), 401

@auth_bp.route('/profile', methods=['GET'])
@jwt_required()
def get_profile():
    """获取用户信息"""
    user_id = get_jwt_identity()
    user = User.query.get(user_id)
    
    if not user:
        return jsonify({"success": False, "message": "用户不存在"}), 404
    
    return jsonify({
        "success": True,
        "user": user.to_dict()
    }), 200

@auth_bp.route('/logout', methods=['POST'])
@jwt_required()
def logout():
    """用户登出"""
    # JWT是无状态的，客户端删除token即可
    return jsonify({"success": True, "message": "登出成功"}), 200 