{"ast": null, "code": "function ownKeys(object, enumerableOnly) {\n  var keys = Object.keys(object);\n  if (Object.getOwnPropertySymbols) {\n    var symbols = Object.getOwnPropertySymbols(object);\n    enumerableOnly && (symbols = symbols.filter(function (sym) {\n      return Object.getOwnPropertyDescriptor(object, sym).enumerable;\n    })), keys.push.apply(keys, symbols);\n  }\n  return keys;\n}\nfunction _objectSpread(target) {\n  for (var i = 1; i < arguments.length; i++) {\n    var source = null != arguments[i] ? arguments[i] : {};\n    i % 2 ? ownKeys(Object(source), !0).forEach(function (key) {\n      _defineProperty(target, key, source[key]);\n    }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(target, Object.getOwnPropertyDescriptors(source)) : ownKeys(Object(source)).forEach(function (key) {\n      Object.defineProperty(target, key, Object.getOwnPropertyDescriptor(source, key));\n    });\n  }\n  return target;\n}\nfunction _defineProperty(obj, key, value) {\n  key = _toPropertyKey(key);\n  if (key in obj) {\n    Object.defineProperty(obj, key, {\n      value: value,\n      enumerable: true,\n      configurable: true,\n      writable: true\n    });\n  } else {\n    obj[key] = value;\n  }\n  return obj;\n}\nfunction _toPropertyKey(arg) {\n  var key = _toPrimitive(arg, \"string\");\n  return typeof key === \"symbol\" ? key : String(key);\n}\nfunction _toPrimitive(input, hint) {\n  if (typeof input !== \"object\" || input === null) return input;\n  var prim = input[Symbol.toPrimitive];\n  if (prim !== undefined) {\n    var res = prim.call(input, hint || \"default\");\n    if (typeof res !== \"object\") return res;\n    throw new TypeError(\"@@toPrimitive must return a primitive value.\");\n  }\n  return (hint === \"string\" ? String : Number)(input);\n}\n// The error overlay is inspired (and mostly copied) from Create React App (https://github.com/facebookincubator/create-react-app)\n// They, in turn, got inspired by webpack-hot-middleware (https://github.com/glenjamin/webpack-hot-middleware).\n\nimport ansiHTML from \"ansi-html-community\";\nimport { encode } from \"html-entities\";\nimport { listenToRuntimeError, listenToUnhandledRejection, parseErrorToStacks } from \"./overlay/runtime-error.js\";\nimport createOverlayMachine from \"./overlay/state-machine.js\";\nimport { containerStyle, dismissButtonStyle, headerStyle, iframeStyle, msgStyles, msgTextStyle, msgTypeStyle } from \"./overlay/styles.js\";\nvar colors = {\n  reset: [\"transparent\", \"transparent\"],\n  black: \"181818\",\n  red: \"E36049\",\n  green: \"B3CB74\",\n  yellow: \"FFD080\",\n  blue: \"7CAFC2\",\n  magenta: \"7FACCA\",\n  cyan: \"C3C2EF\",\n  lightgrey: \"EBE7E3\",\n  darkgrey: \"6D7891\"\n};\nansiHTML.setColors(colors);\n\n/**\n * @param {string} type\n * @param {string  | { file?: string, moduleName?: string, loc?: string, message?: string; stack?: string[] }} item\n * @returns {{ header: string, body: string }}\n */\nfunction formatProblem(type, item) {\n  var header = type === \"warning\" ? \"WARNING\" : \"ERROR\";\n  var body = \"\";\n  if (typeof item === \"string\") {\n    body += item;\n  } else {\n    var file = item.file || \"\";\n    // eslint-disable-next-line no-nested-ternary\n    var moduleName = item.moduleName ? item.moduleName.indexOf(\"!\") !== -1 ? \"\".concat(item.moduleName.replace(/^(\\s|\\S)*!/, \"\"), \" (\").concat(item.moduleName, \")\") : \"\".concat(item.moduleName) : \"\";\n    var loc = item.loc;\n    header += \"\".concat(moduleName || file ? \" in \".concat(moduleName ? \"\".concat(moduleName).concat(file ? \" (\".concat(file, \")\") : \"\") : file).concat(loc ? \" \".concat(loc) : \"\") : \"\");\n    body += item.message || \"\";\n  }\n  if (Array.isArray(item.stack)) {\n    item.stack.forEach(function (stack) {\n      if (typeof stack === \"string\") {\n        body += \"\\r\\n\".concat(stack);\n      }\n    });\n  }\n  return {\n    header: header,\n    body: body\n  };\n}\n\n/**\n * @typedef {Object} CreateOverlayOptions\n * @property {string | null} trustedTypesPolicyName\n * @property {boolean | (error: Error) => void} [catchRuntimeError]\n */\n\n/**\n *\n * @param {CreateOverlayOptions} options\n */\nvar createOverlay = function createOverlay(options) {\n  /** @type {HTMLIFrameElement | null | undefined} */\n  var iframeContainerElement;\n  /** @type {HTMLDivElement | null | undefined} */\n  var containerElement;\n  /** @type {HTMLDivElement | null | undefined} */\n  var headerElement;\n  /** @type {Array<(element: HTMLDivElement) => void>} */\n  var onLoadQueue = [];\n  /** @type {TrustedTypePolicy | undefined} */\n  var overlayTrustedTypesPolicy;\n\n  /**\n   *\n   * @param {HTMLElement} element\n   * @param {CSSStyleDeclaration} style\n   */\n  function applyStyle(element, style) {\n    Object.keys(style).forEach(function (prop) {\n      element.style[prop] = style[prop];\n    });\n  }\n\n  /**\n   * @param {string | null} trustedTypesPolicyName\n   */\n  function createContainer(trustedTypesPolicyName) {\n    // Enable Trusted Types if they are available in the current browser.\n    if (window.trustedTypes) {\n      overlayTrustedTypesPolicy = window.trustedTypes.createPolicy(trustedTypesPolicyName || \"webpack-dev-server#overlay\", {\n        createHTML: function createHTML(value) {\n          return value;\n        }\n      });\n    }\n    iframeContainerElement = document.createElement(\"iframe\");\n    iframeContainerElement.id = \"webpack-dev-server-client-overlay\";\n    iframeContainerElement.src = \"about:blank\";\n    applyStyle(iframeContainerElement, iframeStyle);\n    iframeContainerElement.onload = function () {\n      var contentElement = /** @type {Document} */\n      /** @type {HTMLIFrameElement} */\n      iframeContainerElement.contentDocument.createElement(\"div\");\n      containerElement = /** @type {Document} */\n      /** @type {HTMLIFrameElement} */\n      iframeContainerElement.contentDocument.createElement(\"div\");\n      contentElement.id = \"webpack-dev-server-client-overlay-div\";\n      applyStyle(contentElement, containerStyle);\n      headerElement = document.createElement(\"div\");\n      headerElement.innerText = \"Compiled with problems:\";\n      applyStyle(headerElement, headerStyle);\n      var closeButtonElement = document.createElement(\"button\");\n      applyStyle(closeButtonElement, dismissButtonStyle);\n      closeButtonElement.innerText = \"×\";\n      closeButtonElement.ariaLabel = \"Dismiss\";\n      closeButtonElement.addEventListener(\"click\", function () {\n        // eslint-disable-next-line no-use-before-define\n        overlayService.send({\n          type: \"DISMISS\"\n        });\n      });\n      contentElement.appendChild(headerElement);\n      contentElement.appendChild(closeButtonElement);\n      contentElement.appendChild(containerElement);\n\n      /** @type {Document} */\n      /** @type {HTMLIFrameElement} */\n      iframeContainerElement.contentDocument.body.appendChild(contentElement);\n      onLoadQueue.forEach(function (onLoad) {\n        onLoad(/** @type {HTMLDivElement} */contentElement);\n      });\n      onLoadQueue = [];\n\n      /** @type {HTMLIFrameElement} */\n      iframeContainerElement.onload = null;\n    };\n    document.body.appendChild(iframeContainerElement);\n  }\n\n  /**\n   * @param {(element: HTMLDivElement) => void} callback\n   * @param {string | null} trustedTypesPolicyName\n   */\n  function ensureOverlayExists(callback, trustedTypesPolicyName) {\n    if (containerElement) {\n      containerElement.innerHTML = \"\";\n      // Everything is ready, call the callback right away.\n      callback(containerElement);\n      return;\n    }\n    onLoadQueue.push(callback);\n    if (iframeContainerElement) {\n      return;\n    }\n    createContainer(trustedTypesPolicyName);\n  }\n\n  // Successful compilation.\n  function hide() {\n    if (!iframeContainerElement) {\n      return;\n    }\n\n    // Clean up and reset internal state.\n    document.body.removeChild(iframeContainerElement);\n    iframeContainerElement = null;\n    containerElement = null;\n  }\n\n  // Compilation with errors (e.g. syntax error or missing modules).\n  /**\n   * @param {string} type\n   * @param {Array<string  | { moduleIdentifier?: string, moduleName?: string, loc?: string, message?: string }>} messages\n   * @param {string | null} trustedTypesPolicyName\n   * @param {'build' | 'runtime'} messageSource\n   */\n  function show(type, messages, trustedTypesPolicyName, messageSource) {\n    ensureOverlayExists(function () {\n      headerElement.innerText = messageSource === \"runtime\" ? \"Uncaught runtime errors:\" : \"Compiled with problems:\";\n      messages.forEach(function (message) {\n        var entryElement = document.createElement(\"div\");\n        var msgStyle = type === \"warning\" ? msgStyles.warning : msgStyles.error;\n        applyStyle(entryElement, _objectSpread(_objectSpread({}, msgStyle), {}, {\n          padding: \"1rem 1rem 1.5rem 1rem\"\n        }));\n        var typeElement = document.createElement(\"div\");\n        var _formatProblem = formatProblem(type, message),\n          header = _formatProblem.header,\n          body = _formatProblem.body;\n        typeElement.innerText = header;\n        applyStyle(typeElement, msgTypeStyle);\n        if (message.moduleIdentifier) {\n          applyStyle(typeElement, {\n            cursor: \"pointer\"\n          });\n          // element.dataset not supported in IE\n          typeElement.setAttribute(\"data-can-open\", true);\n          typeElement.addEventListener(\"click\", function () {\n            fetch(\"/webpack-dev-server/open-editor?fileName=\".concat(message.moduleIdentifier));\n          });\n        }\n\n        // Make it look similar to our terminal.\n        var text = ansiHTML(encode(body));\n        var messageTextNode = document.createElement(\"div\");\n        applyStyle(messageTextNode, msgTextStyle);\n        messageTextNode.innerHTML = overlayTrustedTypesPolicy ? overlayTrustedTypesPolicy.createHTML(text) : text;\n        entryElement.appendChild(typeElement);\n        entryElement.appendChild(messageTextNode);\n\n        /** @type {HTMLDivElement} */\n        containerElement.appendChild(entryElement);\n      });\n    }, trustedTypesPolicyName);\n  }\n  var overlayService = createOverlayMachine({\n    showOverlay: function showOverlay(_ref) {\n      var _ref$level = _ref.level,\n        level = _ref$level === void 0 ? \"error\" : _ref$level,\n        messages = _ref.messages,\n        messageSource = _ref.messageSource;\n      return show(level, messages, options.trustedTypesPolicyName, messageSource);\n    },\n    hideOverlay: hide\n  });\n  if (options.catchRuntimeError) {\n    /**\n     * @param {Error | undefined} error\n     * @param {string} fallbackMessage\n     */\n    var handleError = function handleError(error, fallbackMessage) {\n      var errorObject = error instanceof Error ? error : new Error(error || fallbackMessage);\n      var shouldDisplay = typeof options.catchRuntimeError === \"function\" ? options.catchRuntimeError(errorObject) : true;\n      if (shouldDisplay) {\n        overlayService.send({\n          type: \"RUNTIME_ERROR\",\n          messages: [{\n            message: errorObject.message,\n            stack: parseErrorToStacks(errorObject)\n          }]\n        });\n      }\n    };\n    listenToRuntimeError(function (errorEvent) {\n      // error property may be empty in older browser like IE\n      var error = errorEvent.error,\n        message = errorEvent.message;\n      if (!error && !message) {\n        return;\n      }\n      handleError(error, message);\n    });\n    listenToUnhandledRejection(function (promiseRejectionEvent) {\n      var reason = promiseRejectionEvent.reason;\n      handleError(reason, \"Unknown promise rejection reason\");\n    });\n  }\n  return overlayService;\n};\nexport { formatProblem, createOverlay };", "map": {"version": 3, "names": ["ownKeys", "object", "enumerableOnly", "keys", "Object", "getOwnPropertySymbols", "symbols", "filter", "sym", "getOwnPropertyDescriptor", "enumerable", "push", "apply", "_objectSpread", "target", "i", "arguments", "length", "source", "for<PERSON>ach", "key", "_defineProperty", "getOwnPropertyDescriptors", "defineProperties", "defineProperty", "obj", "value", "_to<PERSON><PERSON><PERSON><PERSON><PERSON>", "configurable", "writable", "arg", "_toPrimitive", "String", "input", "hint", "prim", "Symbol", "toPrimitive", "undefined", "res", "call", "TypeError", "Number", "ansiHTML", "encode", "listenToRuntimeError", "listenToUnhandledRejection", "parseErrorToStacks", "createOverlayMachine", "containerStyle", "dismissButtonStyle", "headerStyle", "iframeStyle", "msgStyles", "msgTextStyle", "msgTypeStyle", "colors", "reset", "black", "red", "green", "yellow", "blue", "magenta", "cyan", "<PERSON><PERSON>rey", "<PERSON><PERSON>rey", "setColors", "formatProblem", "type", "item", "header", "body", "file", "moduleName", "indexOf", "concat", "replace", "loc", "message", "Array", "isArray", "stack", "createOverlay", "options", "iframeContainerElement", "containerElement", "headerElement", "onLoadQueue", "overlayTrustedTypesPolicy", "applyStyle", "element", "style", "prop", "createContainer", "trustedTypesPolicyName", "window", "trustedTypes", "createPolicy", "createHTML", "document", "createElement", "id", "src", "onload", "contentElement", "contentDocument", "innerText", "closeButtonElement", "aria<PERSON><PERSON><PERSON>", "addEventListener", "overlayService", "send", "append<PERSON><PERSON><PERSON>", "onLoad", "ensureOverlayExists", "callback", "innerHTML", "hide", "<PERSON><PERSON><PERSON><PERSON>", "show", "messages", "messageSource", "entryElement", "msgStyle", "warning", "error", "padding", "typeElement", "_formatProblem", "moduleIdentifier", "cursor", "setAttribute", "fetch", "text", "messageTextNode", "showOverlay", "_ref", "_ref$level", "level", "hideOverlay", "catchRuntimeError", "handleError", "fallbackMessage", "errorObject", "Error", "shouldDisplay", "errorEvent", "promiseRejectionEvent", "reason"], "sources": ["D:/work/python_work/Family_Takeout/frontend/node_modules/webpack-dev-server/client/overlay.js"], "sourcesContent": ["function ownKeys(object, enumerableOnly) { var keys = Object.keys(object); if (Object.getOwnPropertySymbols) { var symbols = Object.getOwnPropertySymbols(object); enumerableOnly && (symbols = symbols.filter(function (sym) { return Object.getOwnPropertyDescriptor(object, sym).enumerable; })), keys.push.apply(keys, symbols); } return keys; }\nfunction _objectSpread(target) { for (var i = 1; i < arguments.length; i++) { var source = null != arguments[i] ? arguments[i] : {}; i % 2 ? ownKeys(Object(source), !0).forEach(function (key) { _defineProperty(target, key, source[key]); }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(target, Object.getOwnPropertyDescriptors(source)) : ownKeys(Object(source)).forEach(function (key) { Object.defineProperty(target, key, Object.getOwnPropertyDescriptor(source, key)); }); } return target; }\nfunction _defineProperty(obj, key, value) { key = _toPropertyKey(key); if (key in obj) { Object.defineProperty(obj, key, { value: value, enumerable: true, configurable: true, writable: true }); } else { obj[key] = value; } return obj; }\nfunction _toPropertyKey(arg) { var key = _toPrimitive(arg, \"string\"); return typeof key === \"symbol\" ? key : String(key); }\nfunction _toPrimitive(input, hint) { if (typeof input !== \"object\" || input === null) return input; var prim = input[Symbol.toPrimitive]; if (prim !== undefined) { var res = prim.call(input, hint || \"default\"); if (typeof res !== \"object\") return res; throw new TypeError(\"@@toPrimitive must return a primitive value.\"); } return (hint === \"string\" ? String : Number)(input); }\n// The error overlay is inspired (and mostly copied) from Create React App (https://github.com/facebookincubator/create-react-app)\n// They, in turn, got inspired by webpack-hot-middleware (https://github.com/glenjamin/webpack-hot-middleware).\n\nimport ansiHTML from \"ansi-html-community\";\nimport { encode } from \"html-entities\";\nimport { listenToRuntimeError, listenToUnhandledRejection, parseErrorToStacks } from \"./overlay/runtime-error.js\";\nimport createOverlayMachine from \"./overlay/state-machine.js\";\nimport { containerStyle, dismissButtonStyle, headerStyle, iframeStyle, msgStyles, msgTextStyle, msgTypeStyle } from \"./overlay/styles.js\";\nvar colors = {\n  reset: [\"transparent\", \"transparent\"],\n  black: \"181818\",\n  red: \"E36049\",\n  green: \"B3CB74\",\n  yellow: \"FFD080\",\n  blue: \"7CAFC2\",\n  magenta: \"7FACCA\",\n  cyan: \"C3C2EF\",\n  lightgrey: \"EBE7E3\",\n  darkgrey: \"6D7891\"\n};\nansiHTML.setColors(colors);\n\n/**\n * @param {string} type\n * @param {string  | { file?: string, moduleName?: string, loc?: string, message?: string; stack?: string[] }} item\n * @returns {{ header: string, body: string }}\n */\nfunction formatProblem(type, item) {\n  var header = type === \"warning\" ? \"WARNING\" : \"ERROR\";\n  var body = \"\";\n  if (typeof item === \"string\") {\n    body += item;\n  } else {\n    var file = item.file || \"\";\n    // eslint-disable-next-line no-nested-ternary\n    var moduleName = item.moduleName ? item.moduleName.indexOf(\"!\") !== -1 ? \"\".concat(item.moduleName.replace(/^(\\s|\\S)*!/, \"\"), \" (\").concat(item.moduleName, \")\") : \"\".concat(item.moduleName) : \"\";\n    var loc = item.loc;\n    header += \"\".concat(moduleName || file ? \" in \".concat(moduleName ? \"\".concat(moduleName).concat(file ? \" (\".concat(file, \")\") : \"\") : file).concat(loc ? \" \".concat(loc) : \"\") : \"\");\n    body += item.message || \"\";\n  }\n  if (Array.isArray(item.stack)) {\n    item.stack.forEach(function (stack) {\n      if (typeof stack === \"string\") {\n        body += \"\\r\\n\".concat(stack);\n      }\n    });\n  }\n  return {\n    header: header,\n    body: body\n  };\n}\n\n/**\n * @typedef {Object} CreateOverlayOptions\n * @property {string | null} trustedTypesPolicyName\n * @property {boolean | (error: Error) => void} [catchRuntimeError]\n */\n\n/**\n *\n * @param {CreateOverlayOptions} options\n */\nvar createOverlay = function createOverlay(options) {\n  /** @type {HTMLIFrameElement | null | undefined} */\n  var iframeContainerElement;\n  /** @type {HTMLDivElement | null | undefined} */\n  var containerElement;\n  /** @type {HTMLDivElement | null | undefined} */\n  var headerElement;\n  /** @type {Array<(element: HTMLDivElement) => void>} */\n  var onLoadQueue = [];\n  /** @type {TrustedTypePolicy | undefined} */\n  var overlayTrustedTypesPolicy;\n\n  /**\n   *\n   * @param {HTMLElement} element\n   * @param {CSSStyleDeclaration} style\n   */\n  function applyStyle(element, style) {\n    Object.keys(style).forEach(function (prop) {\n      element.style[prop] = style[prop];\n    });\n  }\n\n  /**\n   * @param {string | null} trustedTypesPolicyName\n   */\n  function createContainer(trustedTypesPolicyName) {\n    // Enable Trusted Types if they are available in the current browser.\n    if (window.trustedTypes) {\n      overlayTrustedTypesPolicy = window.trustedTypes.createPolicy(trustedTypesPolicyName || \"webpack-dev-server#overlay\", {\n        createHTML: function createHTML(value) {\n          return value;\n        }\n      });\n    }\n    iframeContainerElement = document.createElement(\"iframe\");\n    iframeContainerElement.id = \"webpack-dev-server-client-overlay\";\n    iframeContainerElement.src = \"about:blank\";\n    applyStyle(iframeContainerElement, iframeStyle);\n    iframeContainerElement.onload = function () {\n      var contentElement = /** @type {Document} */\n      /** @type {HTMLIFrameElement} */\n      iframeContainerElement.contentDocument.createElement(\"div\");\n      containerElement = /** @type {Document} */\n      /** @type {HTMLIFrameElement} */\n      iframeContainerElement.contentDocument.createElement(\"div\");\n      contentElement.id = \"webpack-dev-server-client-overlay-div\";\n      applyStyle(contentElement, containerStyle);\n      headerElement = document.createElement(\"div\");\n      headerElement.innerText = \"Compiled with problems:\";\n      applyStyle(headerElement, headerStyle);\n      var closeButtonElement = document.createElement(\"button\");\n      applyStyle(closeButtonElement, dismissButtonStyle);\n      closeButtonElement.innerText = \"×\";\n      closeButtonElement.ariaLabel = \"Dismiss\";\n      closeButtonElement.addEventListener(\"click\", function () {\n        // eslint-disable-next-line no-use-before-define\n        overlayService.send({\n          type: \"DISMISS\"\n        });\n      });\n      contentElement.appendChild(headerElement);\n      contentElement.appendChild(closeButtonElement);\n      contentElement.appendChild(containerElement);\n\n      /** @type {Document} */\n      /** @type {HTMLIFrameElement} */\n      iframeContainerElement.contentDocument.body.appendChild(contentElement);\n      onLoadQueue.forEach(function (onLoad) {\n        onLoad( /** @type {HTMLDivElement} */contentElement);\n      });\n      onLoadQueue = [];\n\n      /** @type {HTMLIFrameElement} */\n      iframeContainerElement.onload = null;\n    };\n    document.body.appendChild(iframeContainerElement);\n  }\n\n  /**\n   * @param {(element: HTMLDivElement) => void} callback\n   * @param {string | null} trustedTypesPolicyName\n   */\n  function ensureOverlayExists(callback, trustedTypesPolicyName) {\n    if (containerElement) {\n      containerElement.innerHTML = \"\";\n      // Everything is ready, call the callback right away.\n      callback(containerElement);\n      return;\n    }\n    onLoadQueue.push(callback);\n    if (iframeContainerElement) {\n      return;\n    }\n    createContainer(trustedTypesPolicyName);\n  }\n\n  // Successful compilation.\n  function hide() {\n    if (!iframeContainerElement) {\n      return;\n    }\n\n    // Clean up and reset internal state.\n    document.body.removeChild(iframeContainerElement);\n    iframeContainerElement = null;\n    containerElement = null;\n  }\n\n  // Compilation with errors (e.g. syntax error or missing modules).\n  /**\n   * @param {string} type\n   * @param {Array<string  | { moduleIdentifier?: string, moduleName?: string, loc?: string, message?: string }>} messages\n   * @param {string | null} trustedTypesPolicyName\n   * @param {'build' | 'runtime'} messageSource\n   */\n  function show(type, messages, trustedTypesPolicyName, messageSource) {\n    ensureOverlayExists(function () {\n      headerElement.innerText = messageSource === \"runtime\" ? \"Uncaught runtime errors:\" : \"Compiled with problems:\";\n      messages.forEach(function (message) {\n        var entryElement = document.createElement(\"div\");\n        var msgStyle = type === \"warning\" ? msgStyles.warning : msgStyles.error;\n        applyStyle(entryElement, _objectSpread(_objectSpread({}, msgStyle), {}, {\n          padding: \"1rem 1rem 1.5rem 1rem\"\n        }));\n        var typeElement = document.createElement(\"div\");\n        var _formatProblem = formatProblem(type, message),\n          header = _formatProblem.header,\n          body = _formatProblem.body;\n        typeElement.innerText = header;\n        applyStyle(typeElement, msgTypeStyle);\n        if (message.moduleIdentifier) {\n          applyStyle(typeElement, {\n            cursor: \"pointer\"\n          });\n          // element.dataset not supported in IE\n          typeElement.setAttribute(\"data-can-open\", true);\n          typeElement.addEventListener(\"click\", function () {\n            fetch(\"/webpack-dev-server/open-editor?fileName=\".concat(message.moduleIdentifier));\n          });\n        }\n\n        // Make it look similar to our terminal.\n        var text = ansiHTML(encode(body));\n        var messageTextNode = document.createElement(\"div\");\n        applyStyle(messageTextNode, msgTextStyle);\n        messageTextNode.innerHTML = overlayTrustedTypesPolicy ? overlayTrustedTypesPolicy.createHTML(text) : text;\n        entryElement.appendChild(typeElement);\n        entryElement.appendChild(messageTextNode);\n\n        /** @type {HTMLDivElement} */\n        containerElement.appendChild(entryElement);\n      });\n    }, trustedTypesPolicyName);\n  }\n  var overlayService = createOverlayMachine({\n    showOverlay: function showOverlay(_ref) {\n      var _ref$level = _ref.level,\n        level = _ref$level === void 0 ? \"error\" : _ref$level,\n        messages = _ref.messages,\n        messageSource = _ref.messageSource;\n      return show(level, messages, options.trustedTypesPolicyName, messageSource);\n    },\n    hideOverlay: hide\n  });\n  if (options.catchRuntimeError) {\n    /**\n     * @param {Error | undefined} error\n     * @param {string} fallbackMessage\n     */\n    var handleError = function handleError(error, fallbackMessage) {\n      var errorObject = error instanceof Error ? error : new Error(error || fallbackMessage);\n      var shouldDisplay = typeof options.catchRuntimeError === \"function\" ? options.catchRuntimeError(errorObject) : true;\n      if (shouldDisplay) {\n        overlayService.send({\n          type: \"RUNTIME_ERROR\",\n          messages: [{\n            message: errorObject.message,\n            stack: parseErrorToStacks(errorObject)\n          }]\n        });\n      }\n    };\n    listenToRuntimeError(function (errorEvent) {\n      // error property may be empty in older browser like IE\n      var error = errorEvent.error,\n        message = errorEvent.message;\n      if (!error && !message) {\n        return;\n      }\n      handleError(error, message);\n    });\n    listenToUnhandledRejection(function (promiseRejectionEvent) {\n      var reason = promiseRejectionEvent.reason;\n      handleError(reason, \"Unknown promise rejection reason\");\n    });\n  }\n  return overlayService;\n};\nexport { formatProblem, createOverlay };"], "mappings": "AAAA,SAASA,OAAOA,CAACC,MAAM,EAAEC,cAAc,EAAE;EAAE,IAAIC,IAAI,GAAGC,MAAM,CAACD,IAAI,CAACF,MAAM,CAAC;EAAE,IAAIG,MAAM,CAACC,qBAAqB,EAAE;IAAE,IAAIC,OAAO,GAAGF,MAAM,CAACC,qBAAqB,CAACJ,MAAM,CAAC;IAAEC,cAAc,KAAKI,OAAO,GAAGA,OAAO,CAACC,MAAM,CAAC,UAAUC,GAAG,EAAE;MAAE,OAAOJ,MAAM,CAACK,wBAAwB,CAACR,MAAM,EAAEO,GAAG,CAAC,CAACE,UAAU;IAAE,CAAC,CAAC,CAAC,EAAEP,IAAI,CAACQ,IAAI,CAACC,KAAK,CAACT,IAAI,EAAEG,OAAO,CAAC;EAAE;EAAE,OAAOH,IAAI;AAAE;AACpV,SAASU,aAAaA,CAACC,MAAM,EAAE;EAAE,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGC,SAAS,CAACC,MAAM,EAAEF,CAAC,EAAE,EAAE;IAAE,IAAIG,MAAM,GAAG,IAAI,IAAIF,SAAS,CAACD,CAAC,CAAC,GAAGC,SAAS,CAACD,CAAC,CAAC,GAAG,CAAC,CAAC;IAAEA,CAAC,GAAG,CAAC,GAAGf,OAAO,CAACI,MAAM,CAACc,MAAM,CAAC,EAAE,CAAC,CAAC,CAAC,CAACC,OAAO,CAAC,UAAUC,GAAG,EAAE;MAAEC,eAAe,CAACP,MAAM,EAAEM,GAAG,EAAEF,MAAM,CAACE,GAAG,CAAC,CAAC;IAAE,CAAC,CAAC,GAAGhB,MAAM,CAACkB,yBAAyB,GAAGlB,MAAM,CAACmB,gBAAgB,CAACT,MAAM,EAAEV,MAAM,CAACkB,yBAAyB,CAACJ,MAAM,CAAC,CAAC,GAAGlB,OAAO,CAACI,MAAM,CAACc,MAAM,CAAC,CAAC,CAACC,OAAO,CAAC,UAAUC,GAAG,EAAE;MAAEhB,MAAM,CAACoB,cAAc,CAACV,MAAM,EAAEM,GAAG,EAAEhB,MAAM,CAACK,wBAAwB,CAACS,MAAM,EAAEE,GAAG,CAAC,CAAC;IAAE,CAAC,CAAC;EAAE;EAAE,OAAON,MAAM;AAAE;AACzf,SAASO,eAAeA,CAACI,GAAG,EAAEL,GAAG,EAAEM,KAAK,EAAE;EAAEN,GAAG,GAAGO,cAAc,CAACP,GAAG,CAAC;EAAE,IAAIA,GAAG,IAAIK,GAAG,EAAE;IAAErB,MAAM,CAACoB,cAAc,CAACC,GAAG,EAAEL,GAAG,EAAE;MAAEM,KAAK,EAAEA,KAAK;MAAEhB,UAAU,EAAE,IAAI;MAAEkB,YAAY,EAAE,IAAI;MAAEC,QAAQ,EAAE;IAAK,CAAC,CAAC;EAAE,CAAC,MAAM;IAAEJ,GAAG,CAACL,GAAG,CAAC,GAAGM,KAAK;EAAE;EAAE,OAAOD,GAAG;AAAE;AAC3O,SAASE,cAAcA,CAACG,GAAG,EAAE;EAAE,IAAIV,GAAG,GAAGW,YAAY,CAACD,GAAG,EAAE,QAAQ,CAAC;EAAE,OAAO,OAAOV,GAAG,KAAK,QAAQ,GAAGA,GAAG,GAAGY,MAAM,CAACZ,GAAG,CAAC;AAAE;AAC1H,SAASW,YAAYA,CAACE,KAAK,EAAEC,IAAI,EAAE;EAAE,IAAI,OAAOD,KAAK,KAAK,QAAQ,IAAIA,KAAK,KAAK,IAAI,EAAE,OAAOA,KAAK;EAAE,IAAIE,IAAI,GAAGF,KAAK,CAACG,MAAM,CAACC,WAAW,CAAC;EAAE,IAAIF,IAAI,KAAKG,SAAS,EAAE;IAAE,IAAIC,GAAG,GAAGJ,IAAI,CAACK,IAAI,CAACP,KAAK,EAAEC,IAAI,IAAI,SAAS,CAAC;IAAE,IAAI,OAAOK,GAAG,KAAK,QAAQ,EAAE,OAAOA,GAAG;IAAE,MAAM,IAAIE,SAAS,CAAC,8CAA8C,CAAC;EAAE;EAAE,OAAO,CAACP,IAAI,KAAK,QAAQ,GAAGF,MAAM,GAAGU,MAAM,EAAET,KAAK,CAAC;AAAE;AACxX;AACA;;AAEA,OAAOU,QAAQ,MAAM,qBAAqB;AAC1C,SAASC,MAAM,QAAQ,eAAe;AACtC,SAASC,oBAAoB,EAAEC,0BAA0B,EAAEC,kBAAkB,QAAQ,4BAA4B;AACjH,OAAOC,oBAAoB,MAAM,4BAA4B;AAC7D,SAASC,cAAc,EAAEC,kBAAkB,EAAEC,WAAW,EAAEC,WAAW,EAAEC,SAAS,EAAEC,YAAY,EAAEC,YAAY,QAAQ,qBAAqB;AACzI,IAAIC,MAAM,GAAG;EACXC,KAAK,EAAE,CAAC,aAAa,EAAE,aAAa,CAAC;EACrCC,KAAK,EAAE,QAAQ;EACfC,GAAG,EAAE,QAAQ;EACbC,KAAK,EAAE,QAAQ;EACfC,MAAM,EAAE,QAAQ;EAChBC,IAAI,EAAE,QAAQ;EACdC,OAAO,EAAE,QAAQ;EACjBC,IAAI,EAAE,QAAQ;EACdC,SAAS,EAAE,QAAQ;EACnBC,QAAQ,EAAE;AACZ,CAAC;AACDvB,QAAQ,CAACwB,SAAS,CAACX,MAAM,CAAC;;AAE1B;AACA;AACA;AACA;AACA;AACA,SAASY,aAAaA,CAACC,IAAI,EAAEC,IAAI,EAAE;EACjC,IAAIC,MAAM,GAAGF,IAAI,KAAK,SAAS,GAAG,SAAS,GAAG,OAAO;EACrD,IAAIG,IAAI,GAAG,EAAE;EACb,IAAI,OAAOF,IAAI,KAAK,QAAQ,EAAE;IAC5BE,IAAI,IAAIF,IAAI;EACd,CAAC,MAAM;IACL,IAAIG,IAAI,GAAGH,IAAI,CAACG,IAAI,IAAI,EAAE;IAC1B;IACA,IAAIC,UAAU,GAAGJ,IAAI,CAACI,UAAU,GAAGJ,IAAI,CAACI,UAAU,CAACC,OAAO,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC,GAAG,EAAE,CAACC,MAAM,CAACN,IAAI,CAACI,UAAU,CAACG,OAAO,CAAC,YAAY,EAAE,EAAE,CAAC,EAAE,IAAI,CAAC,CAACD,MAAM,CAACN,IAAI,CAACI,UAAU,EAAE,GAAG,CAAC,GAAG,EAAE,CAACE,MAAM,CAACN,IAAI,CAACI,UAAU,CAAC,GAAG,EAAE;IAClM,IAAII,GAAG,GAAGR,IAAI,CAACQ,GAAG;IAClBP,MAAM,IAAI,EAAE,CAACK,MAAM,CAACF,UAAU,IAAID,IAAI,GAAG,MAAM,CAACG,MAAM,CAACF,UAAU,GAAG,EAAE,CAACE,MAAM,CAACF,UAAU,CAAC,CAACE,MAAM,CAACH,IAAI,GAAG,IAAI,CAACG,MAAM,CAACH,IAAI,EAAE,GAAG,CAAC,GAAG,EAAE,CAAC,GAAGA,IAAI,CAAC,CAACG,MAAM,CAACE,GAAG,GAAG,GAAG,CAACF,MAAM,CAACE,GAAG,CAAC,GAAG,EAAE,CAAC,GAAG,EAAE,CAAC;IACrLN,IAAI,IAAIF,IAAI,CAACS,OAAO,IAAI,EAAE;EAC5B;EACA,IAAIC,KAAK,CAACC,OAAO,CAACX,IAAI,CAACY,KAAK,CAAC,EAAE;IAC7BZ,IAAI,CAACY,KAAK,CAAC/D,OAAO,CAAC,UAAU+D,KAAK,EAAE;MAClC,IAAI,OAAOA,KAAK,KAAK,QAAQ,EAAE;QAC7BV,IAAI,IAAI,MAAM,CAACI,MAAM,CAACM,KAAK,CAAC;MAC9B;IACF,CAAC,CAAC;EACJ;EACA,OAAO;IACLX,MAAM,EAAEA,MAAM;IACdC,IAAI,EAAEA;EACR,CAAC;AACH;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA,IAAIW,aAAa,GAAG,SAASA,aAAaA,CAACC,OAAO,EAAE;EAClD;EACA,IAAIC,sBAAsB;EAC1B;EACA,IAAIC,gBAAgB;EACpB;EACA,IAAIC,aAAa;EACjB;EACA,IAAIC,WAAW,GAAG,EAAE;EACpB;EACA,IAAIC,yBAAyB;;EAE7B;AACF;AACA;AACA;AACA;EACE,SAASC,UAAUA,CAACC,OAAO,EAAEC,KAAK,EAAE;IAClCxF,MAAM,CAACD,IAAI,CAACyF,KAAK,CAAC,CAACzE,OAAO,CAAC,UAAU0E,IAAI,EAAE;MACzCF,OAAO,CAACC,KAAK,CAACC,IAAI,CAAC,GAAGD,KAAK,CAACC,IAAI,CAAC;IACnC,CAAC,CAAC;EACJ;;EAEA;AACF;AACA;EACE,SAASC,eAAeA,CAACC,sBAAsB,EAAE;IAC/C;IACA,IAAIC,MAAM,CAACC,YAAY,EAAE;MACvBR,yBAAyB,GAAGO,MAAM,CAACC,YAAY,CAACC,YAAY,CAACH,sBAAsB,IAAI,4BAA4B,EAAE;QACnHI,UAAU,EAAE,SAASA,UAAUA,CAACzE,KAAK,EAAE;UACrC,OAAOA,KAAK;QACd;MACF,CAAC,CAAC;IACJ;IACA2D,sBAAsB,GAAGe,QAAQ,CAACC,aAAa,CAAC,QAAQ,CAAC;IACzDhB,sBAAsB,CAACiB,EAAE,GAAG,mCAAmC;IAC/DjB,sBAAsB,CAACkB,GAAG,GAAG,aAAa;IAC1Cb,UAAU,CAACL,sBAAsB,EAAEjC,WAAW,CAAC;IAC/CiC,sBAAsB,CAACmB,MAAM,GAAG,YAAY;MAC1C,IAAIC,cAAc,GAAG;MACrB;MACApB,sBAAsB,CAACqB,eAAe,CAACL,aAAa,CAAC,KAAK,CAAC;MAC3Df,gBAAgB,GAAG;MACnB;MACAD,sBAAsB,CAACqB,eAAe,CAACL,aAAa,CAAC,KAAK,CAAC;MAC3DI,cAAc,CAACH,EAAE,GAAG,uCAAuC;MAC3DZ,UAAU,CAACe,cAAc,EAAExD,cAAc,CAAC;MAC1CsC,aAAa,GAAGa,QAAQ,CAACC,aAAa,CAAC,KAAK,CAAC;MAC7Cd,aAAa,CAACoB,SAAS,GAAG,yBAAyB;MACnDjB,UAAU,CAACH,aAAa,EAAEpC,WAAW,CAAC;MACtC,IAAIyD,kBAAkB,GAAGR,QAAQ,CAACC,aAAa,CAAC,QAAQ,CAAC;MACzDX,UAAU,CAACkB,kBAAkB,EAAE1D,kBAAkB,CAAC;MAClD0D,kBAAkB,CAACD,SAAS,GAAG,GAAG;MAClCC,kBAAkB,CAACC,SAAS,GAAG,SAAS;MACxCD,kBAAkB,CAACE,gBAAgB,CAAC,OAAO,EAAE,YAAY;QACvD;QACAC,cAAc,CAACC,IAAI,CAAC;UAClB3C,IAAI,EAAE;QACR,CAAC,CAAC;MACJ,CAAC,CAAC;MACFoC,cAAc,CAACQ,WAAW,CAAC1B,aAAa,CAAC;MACzCkB,cAAc,CAACQ,WAAW,CAACL,kBAAkB,CAAC;MAC9CH,cAAc,CAACQ,WAAW,CAAC3B,gBAAgB,CAAC;;MAE5C;MACA;MACAD,sBAAsB,CAACqB,eAAe,CAAClC,IAAI,CAACyC,WAAW,CAACR,cAAc,CAAC;MACvEjB,WAAW,CAACrE,OAAO,CAAC,UAAU+F,MAAM,EAAE;QACpCA,MAAM,CAAE,6BAA6BT,cAAc,CAAC;MACtD,CAAC,CAAC;MACFjB,WAAW,GAAG,EAAE;;MAEhB;MACAH,sBAAsB,CAACmB,MAAM,GAAG,IAAI;IACtC,CAAC;IACDJ,QAAQ,CAAC5B,IAAI,CAACyC,WAAW,CAAC5B,sBAAsB,CAAC;EACnD;;EAEA;AACF;AACA;AACA;EACE,SAAS8B,mBAAmBA,CAACC,QAAQ,EAAErB,sBAAsB,EAAE;IAC7D,IAAIT,gBAAgB,EAAE;MACpBA,gBAAgB,CAAC+B,SAAS,GAAG,EAAE;MAC/B;MACAD,QAAQ,CAAC9B,gBAAgB,CAAC;MAC1B;IACF;IACAE,WAAW,CAAC7E,IAAI,CAACyG,QAAQ,CAAC;IAC1B,IAAI/B,sBAAsB,EAAE;MAC1B;IACF;IACAS,eAAe,CAACC,sBAAsB,CAAC;EACzC;;EAEA;EACA,SAASuB,IAAIA,CAAA,EAAG;IACd,IAAI,CAACjC,sBAAsB,EAAE;MAC3B;IACF;;IAEA;IACAe,QAAQ,CAAC5B,IAAI,CAAC+C,WAAW,CAAClC,sBAAsB,CAAC;IACjDA,sBAAsB,GAAG,IAAI;IAC7BC,gBAAgB,GAAG,IAAI;EACzB;;EAEA;EACA;AACF;AACA;AACA;AACA;AACA;EACE,SAASkC,IAAIA,CAACnD,IAAI,EAAEoD,QAAQ,EAAE1B,sBAAsB,EAAE2B,aAAa,EAAE;IACnEP,mBAAmB,CAAC,YAAY;MAC9B5B,aAAa,CAACoB,SAAS,GAAGe,aAAa,KAAK,SAAS,GAAG,0BAA0B,GAAG,yBAAyB;MAC9GD,QAAQ,CAACtG,OAAO,CAAC,UAAU4D,OAAO,EAAE;QAClC,IAAI4C,YAAY,GAAGvB,QAAQ,CAACC,aAAa,CAAC,KAAK,CAAC;QAChD,IAAIuB,QAAQ,GAAGvD,IAAI,KAAK,SAAS,GAAGhB,SAAS,CAACwE,OAAO,GAAGxE,SAAS,CAACyE,KAAK;QACvEpC,UAAU,CAACiC,YAAY,EAAE9G,aAAa,CAACA,aAAa,CAAC,CAAC,CAAC,EAAE+G,QAAQ,CAAC,EAAE,CAAC,CAAC,EAAE;UACtEG,OAAO,EAAE;QACX,CAAC,CAAC,CAAC;QACH,IAAIC,WAAW,GAAG5B,QAAQ,CAACC,aAAa,CAAC,KAAK,CAAC;QAC/C,IAAI4B,cAAc,GAAG7D,aAAa,CAACC,IAAI,EAAEU,OAAO,CAAC;UAC/CR,MAAM,GAAG0D,cAAc,CAAC1D,MAAM;UAC9BC,IAAI,GAAGyD,cAAc,CAACzD,IAAI;QAC5BwD,WAAW,CAACrB,SAAS,GAAGpC,MAAM;QAC9BmB,UAAU,CAACsC,WAAW,EAAEzE,YAAY,CAAC;QACrC,IAAIwB,OAAO,CAACmD,gBAAgB,EAAE;UAC5BxC,UAAU,CAACsC,WAAW,EAAE;YACtBG,MAAM,EAAE;UACV,CAAC,CAAC;UACF;UACAH,WAAW,CAACI,YAAY,CAAC,eAAe,EAAE,IAAI,CAAC;UAC/CJ,WAAW,CAAClB,gBAAgB,CAAC,OAAO,EAAE,YAAY;YAChDuB,KAAK,CAAC,2CAA2C,CAACzD,MAAM,CAACG,OAAO,CAACmD,gBAAgB,CAAC,CAAC;UACrF,CAAC,CAAC;QACJ;;QAEA;QACA,IAAII,IAAI,GAAG3F,QAAQ,CAACC,MAAM,CAAC4B,IAAI,CAAC,CAAC;QACjC,IAAI+D,eAAe,GAAGnC,QAAQ,CAACC,aAAa,CAAC,KAAK,CAAC;QACnDX,UAAU,CAAC6C,eAAe,EAAEjF,YAAY,CAAC;QACzCiF,eAAe,CAAClB,SAAS,GAAG5B,yBAAyB,GAAGA,yBAAyB,CAACU,UAAU,CAACmC,IAAI,CAAC,GAAGA,IAAI;QACzGX,YAAY,CAACV,WAAW,CAACe,WAAW,CAAC;QACrCL,YAAY,CAACV,WAAW,CAACsB,eAAe,CAAC;;QAEzC;QACAjD,gBAAgB,CAAC2B,WAAW,CAACU,YAAY,CAAC;MAC5C,CAAC,CAAC;IACJ,CAAC,EAAE5B,sBAAsB,CAAC;EAC5B;EACA,IAAIgB,cAAc,GAAG/D,oBAAoB,CAAC;IACxCwF,WAAW,EAAE,SAASA,WAAWA,CAACC,IAAI,EAAE;MACtC,IAAIC,UAAU,GAAGD,IAAI,CAACE,KAAK;QACzBA,KAAK,GAAGD,UAAU,KAAK,KAAK,CAAC,GAAG,OAAO,GAAGA,UAAU;QACpDjB,QAAQ,GAAGgB,IAAI,CAAChB,QAAQ;QACxBC,aAAa,GAAGe,IAAI,CAACf,aAAa;MACpC,OAAOF,IAAI,CAACmB,KAAK,EAAElB,QAAQ,EAAErC,OAAO,CAACW,sBAAsB,EAAE2B,aAAa,CAAC;IAC7E,CAAC;IACDkB,WAAW,EAAEtB;EACf,CAAC,CAAC;EACF,IAAIlC,OAAO,CAACyD,iBAAiB,EAAE;IAC7B;AACJ;AACA;AACA;IACI,IAAIC,WAAW,GAAG,SAASA,WAAWA,CAAChB,KAAK,EAAEiB,eAAe,EAAE;MAC7D,IAAIC,WAAW,GAAGlB,KAAK,YAAYmB,KAAK,GAAGnB,KAAK,GAAG,IAAImB,KAAK,CAACnB,KAAK,IAAIiB,eAAe,CAAC;MACtF,IAAIG,aAAa,GAAG,OAAO9D,OAAO,CAACyD,iBAAiB,KAAK,UAAU,GAAGzD,OAAO,CAACyD,iBAAiB,CAACG,WAAW,CAAC,GAAG,IAAI;MACnH,IAAIE,aAAa,EAAE;QACjBnC,cAAc,CAACC,IAAI,CAAC;UAClB3C,IAAI,EAAE,eAAe;UACrBoD,QAAQ,EAAE,CAAC;YACT1C,OAAO,EAAEiE,WAAW,CAACjE,OAAO;YAC5BG,KAAK,EAAEnC,kBAAkB,CAACiG,WAAW;UACvC,CAAC;QACH,CAAC,CAAC;MACJ;IACF,CAAC;IACDnG,oBAAoB,CAAC,UAAUsG,UAAU,EAAE;MACzC;MACA,IAAIrB,KAAK,GAAGqB,UAAU,CAACrB,KAAK;QAC1B/C,OAAO,GAAGoE,UAAU,CAACpE,OAAO;MAC9B,IAAI,CAAC+C,KAAK,IAAI,CAAC/C,OAAO,EAAE;QACtB;MACF;MACA+D,WAAW,CAAChB,KAAK,EAAE/C,OAAO,CAAC;IAC7B,CAAC,CAAC;IACFjC,0BAA0B,CAAC,UAAUsG,qBAAqB,EAAE;MAC1D,IAAIC,MAAM,GAAGD,qBAAqB,CAACC,MAAM;MACzCP,WAAW,CAACO,MAAM,EAAE,kCAAkC,CAAC;IACzD,CAAC,CAAC;EACJ;EACA,OAAOtC,cAAc;AACvB,CAAC;AACD,SAAS3C,aAAa,EAAEe,aAAa", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}